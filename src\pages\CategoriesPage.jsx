import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import DataTable from '../components/tables/DataTable'
import DynamicForm from '../components/forms/DynamicForm'
import ConfirmDialog from '../components/modals/ConfirmDialog'
import { Package, Folder, FolderOpen, Image, Eye, Hash } from 'lucide-react'
import { categoryService } from '@/services'

const CategoriesPage = () => {
  const [showForm, setShowForm] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [editingCategory, setEditingCategory] = useState(null)
  const [deletingCategory, setDeletingCategory] = useState(null)
  const [bulkDeleteIds, setBulkDeleteIds] = useState([])
  
  const queryClient = useQueryClient()

  // Fetch categories
  const { data: categories = [], isLoading, refetch } = useQuery({
    queryKey: ['categories'],
    queryFn: categoryService.getCategories
  })

  // Create category mutation
  const createMutation = useMutation({
    mutationFn: categoryService.createCategory,
    onSuccess: () => {
      queryClient.invalidateQueries(['categories'])
      setShowForm(false)
      setEditingCategory(null)
    }
  })

  // Update category mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, ...data }) => categoryService.updateCategory(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries(['categories'])
      setShowForm(false)
      setEditingCategory(null)
    }
  })

  // Delete category mutation
  const deleteMutation = useMutation({
    mutationFn: categoryService.deleteCategory,
    onSuccess: () => {
      queryClient.invalidateQueries(['categories'])
      setShowDeleteDialog(false)
      setDeletingCategory(null)
    }
  })

  // Bulk delete mutation (implement if Django supports it)
  const bulkDeleteMutation = useMutation({
    mutationFn: async (ids) => {
      // Delete categories one by one since Django might not support bulk delete
      await Promise.all(ids.map(id => categoryService.deleteCategory(id)))
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['categories'])
      setBulkDeleteIds([])
    }
  })

  const parentCategories = categories.filter(cat => !cat.parent_id)

  const columns = [
    {
      key: 'name',
      title: 'Category',
      render: (value, category) => (
        <div className="flex items-center space-x-3">
          {category.image_url ? (
            <img 
              src={category.image_url} 
              alt={value}
              className="h-10 w-10 rounded-lg object-cover"
            />
          ) : (
            <div className="h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center">
              <Package className="h-5 w-5 text-gray-400" />
            </div>
          )}
          <div>
            <div className="flex items-center space-x-2">
              {category.parent_id ? (
                <FolderOpen className="h-4 w-4 text-blue-500" />
              ) : (
                <Folder className="h-4 w-4 text-gray-500" />
              )}
              <span className="font-medium text-gray-900">{value}</span>
            </div>
            <div className="text-sm text-gray-500">{category.description}</div>
          </div>
        </div>
      )
    },
    {
      key: 'parent_id',
      title: 'Parent Category',
      render: (value) => {
        if (!value) return <span className="text-gray-400">Root Category</span>
        const parent = categories.find(cat => cat.id === value)
        return parent ? parent.name : 'Unknown'
      }
    },
    {
      key: 'subcategories_count',
      title: 'Subcategories',
      render: (value) => (
        <div className="flex items-center space-x-1">
          <Hash className="h-4 w-4 text-gray-400" />
          <span>{value}</span>
        </div>
      )
    },
    {
      key: 'sort_order',
      title: 'Order',
      render: (value) => (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          {value}
        </span>
      )
    },
    {
      key: 'is_active',
      title: 'Status',
      render: (value) => (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          value ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {value ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'created_at',
      title: 'Created',
      type: 'date'
    }
  ]

  const formFields = [
    {
      name: 'name',
      label: 'Category Name',
      type: 'text',
      required: true,
      placeholder: 'Enter category name'
    },
    {
      name: 'parent_id',
      label: 'Parent Category',
      type: 'select',
      options: [
        { value: '', label: 'Root Category (No Parent)' },
        ...parentCategories.map(cat => ({
          value: cat.id,
          label: cat.name
        }))
      ]
    },
    {
      name: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Enter category description',
      fullWidth: true,
      rows: 3
    },
    {
      name: 'image_url',
      label: 'Image URL',
      type: 'url',
      placeholder: 'https://example.com/image.jpg',
      fullWidth: true
    },
    {
      name: 'sort_order',
      label: 'Sort Order',
      type: 'number',
      placeholder: '1',
      validation: (value) => {
        if (value && (isNaN(value) || value < 1)) {
          return 'Sort order must be a positive number'
        }
      }
    },
    {
      name: 'is_active',
      label: 'Active Category',
      type: 'checkbox'
    }
  ]

  const handleAdd = () => {
    setEditingCategory(null)
    setShowForm(true)
  }

  const handleEdit = (category) => {
    setEditingCategory(category)
    setShowForm(true)
  }

  const handleDelete = (category) => {
    setDeletingCategory(category)
    setShowDeleteDialog(true)
  }

  const handleBulkDelete = (ids) => {
    setBulkDeleteIds(ids)
    setShowDeleteDialog(true)
  }

  const handleFormSubmit = (formData) => {
    const processedData = {
      ...formData,
      parent_id: formData.parent_id || null,
      sort_order: parseInt(formData.sort_order) || 1
    }

    if (editingCategory) {
      updateMutation.mutate({ id: editingCategory.id, ...processedData })
    } else {
      createMutation.mutate(processedData)
    }
  }

  const handleConfirmDelete = () => {
    if (bulkDeleteIds.length > 0) {
      bulkDeleteMutation.mutate(bulkDeleteIds)
    } else if (deletingCategory) {
      deleteMutation.mutate(deletingCategory.id)
    }
  }

  const handleExport = () => {
    const csvContent = [
      ['Name', 'Parent', 'Description', 'Status', 'Order', 'Created'].join(','),
      ...categories.map(cat => [
        cat.name,
        cat.parent_id ? categories.find(p => p.id === cat.parent_id)?.name || 'Unknown' : 'Root',
        cat.description,
        cat.is_active ? 'Active' : 'Inactive',
        cat.sort_order,
        new Date(cat.created_at).toLocaleDateString()
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'categories.csv'
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Category Management</h1>
          <p className="text-gray-600">
            Organize services into hierarchical categories
          </p>
        </div>
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <div className="flex items-center space-x-1">
            <Package className="h-4 w-4" />
            <span>Total: {categories.length}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Folder className="h-4 w-4 text-blue-600" />
            <span>Root: {parentCategories.length}</span>
          </div>
          <div className="flex items-center space-x-1">
            <FolderOpen className="h-4 w-4 text-green-600" />
            <span>Active: {categories.filter(c => c.is_active).length}</span>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={categories}
        columns={columns}
        title="Categories"
        loading={isLoading}
        onAdd={handleAdd}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onBulkDelete={handleBulkDelete}
        onExport={handleExport}
        onRefresh={refetch}
        searchable
        sortable
        selectable
        filterable
      />

      {/* Form Modal */}
      <DynamicForm
        isOpen={showForm}
        onClose={() => {
          setShowForm(false)
          setEditingCategory(null)
        }}
        onSubmit={handleFormSubmit}
        title={editingCategory ? 'Edit Category' : 'Add New Category'}
        fields={formFields}
        initialData={editingCategory || { is_active: true, sort_order: 1 }}
        loading={createMutation.isPending || updateMutation.isPending}
        submitText={editingCategory ? 'Update Category' : 'Create Category'}
      />

      {/* Delete Confirmation */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => {
          setShowDeleteDialog(false)
          setDeletingCategory(null)
          setBulkDeleteIds([])
        }}
        onConfirm={handleConfirmDelete}
        title={bulkDeleteIds.length > 0 ? 'Delete Multiple Categories' : 'Delete Category'}
        message={
          bulkDeleteIds.length > 0
            ? `Are you sure you want to delete ${bulkDeleteIds.length} selected categories? This action cannot be undone.`
            : `Are you sure you want to delete "${deletingCategory?.name}"? This will also delete all subcategories and associated services.`
        }
        confirmText="Delete"
        type="danger"
        loading={deleteMutation.isPending || bulkDeleteMutation.isPending}
      />
    </div>
  )
}

export default CategoriesPage

