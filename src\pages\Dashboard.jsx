import { useQuery } from '@tanstack/react-query'
import {
  Users,
  ShoppingCart,
  CreditCard,
  TrendingUp,
  Activity,
  Calendar,
  Clock,
  RefreshCw
} from 'lucide-react'
import { dashboardService } from '@/services'

const StatCard = ({ title, value, change, changeType, icon: Icon }) => {
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change !== undefined && (
            <div className="flex items-center text-xs text-gray-500 mt-1">
              <span className={changeType === 'increase' ? 'text-green-500' : 'text-red-500'}>
                {changeType === 'increase' ? '↗' : '↘'} {Math.abs(change)}%
              </span>
              <span className="ml-1">from last month</span>
            </div>
          )}
        </div>
        <div className="p-3 bg-blue-50 rounded-full">
          <Icon className="h-6 w-6 text-blue-600" />
        </div>
      </div>
    </div>
  )
}

const RecentActivityCard = ({ activities = [] }) => {
  const defaultActivities = [
    { description: 'New user registration', type: 'User', timestamp: '2 hours ago' },
    { description: 'Order #1234 completed', type: 'Order', timestamp: '3 hours ago' },
    { description: 'Provider verification approved', type: 'Provider', timestamp: '5 hours ago' },
    { description: 'Payment processed successfully', type: 'Payment', timestamp: '1 day ago' },
    { description: 'New service category added', type: 'Category', timestamp: '2 days ago' },
  ]

  const displayActivities = activities.length > 0 ? activities : defaultActivities

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
      <div className="flex items-center mb-4">
        <Activity className="h-5 w-5 mr-2 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">Recent Activities</h3>
      </div>
      <div className="space-y-4">
        {displayActivities.map((activity, index) => (
          <div key={index} className="flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900">{activity.description}</p>
              <div className="flex items-center space-x-2 mt-1">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                  {activity.type}
                </span>
                <span className="text-xs text-gray-500">
                  {activity.timestamp}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

const Dashboard = () => {
  // Fetch dashboard data from Django API
  const { data: dashboardData, isLoading, refetch } = useQuery({
    queryKey: ['dashboard'],
    queryFn: dashboardService.getDashboardStats
  })

  // Fetch recent activities
  const { data: activitiesData } = useQuery({
    queryKey: ['dashboard-activities'],
    queryFn: dashboardService.getRecentActivities
  })

  // Default stats if data is not loaded
  const stats = dashboardData || {
    total_orders: 0,
    pending_orders: 0,
    confirmed_orders: 0,
    in_progress_orders: 0,
    completed_orders: 0,
    cancelled_orders: 0,
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Dashboard</h1>
          <p className="text-gray-600">
            Welcome back! Here's what's happening with your home services platform.
          </p>
        </div>
        <button
          onClick={() => refetch()}
          disabled={isLoading}
          className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Orders"
          value={stats.total_orders?.toLocaleString() || '0'}
          icon={ShoppingCart}
        />
        <StatCard
          title="Pending Orders"
          value={stats.pending_orders?.toLocaleString() || '0'}
          icon={Clock}
        />
        <StatCard
          title="Completed Orders"
          value={stats.completed_orders?.toLocaleString() || '0'}
          icon={TrendingUp}
        />
        <StatCard
          title="In Progress"
          value={stats.in_progress_orders?.toLocaleString() || '0'}
          icon={Activity}
        />
      </div>

      {/* Charts and Activities */}
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <RecentActivityCard activities={activitiesData?.activities || []} />
        </div>
        
        {/* Quick Actions */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center mb-4">
            <Clock className="h-5 w-5 mr-2 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          </div>
          <div className="space-y-3">
            <button className="w-full flex items-center justify-start px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              <Users className="h-4 w-4 mr-2" />
              Manage Users
            </button>
            <button className="w-full flex items-center justify-start px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              <ShoppingCart className="h-4 w-4 mr-2" />
              View Orders
            </button>
            <button className="w-full flex items-center justify-start px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              <CreditCard className="h-4 w-4 mr-2" />
              Payment Settings
            </button>
            <button className="w-full flex items-center justify-start px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Management
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard

