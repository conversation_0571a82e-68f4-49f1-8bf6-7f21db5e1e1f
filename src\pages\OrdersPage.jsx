import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import DataTable from '../components/tables/DataTable'
import DynamicForm from '../components/forms/DynamicForm'
import ConfirmDialog from '../components/modals/ConfirmDialog'
import Modal from '../components/modals/Modal'
import {
  Receipt,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  MapPin,
  Calendar,
  IndianRupee,
  Phone,
  Eye
} from 'lucide-react'
import { orderService } from '@/services'

const OrdersPage = () => {
  const [showForm, setShowForm] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [editingOrder, setEditingOrder] = useState(null)
  const [viewingOrder, setViewingOrder] = useState(null)
  const [deletingOrder, setDeletingOrder] = useState(null)
  const [bulkAction, setBulkAction] = useState({ ids: [], action: '' })
  
  const queryClient = useQueryClient()

  // Fetch orders
  const { data: orders = [], isLoading, refetch } = useQuery({
    queryKey: ['orders'],
    queryFn: orderService.getOrders
  })

  // Update order mutation (using updateOrderStatus)
  const updateMutation = useMutation({
    mutationFn: ({ id, status, reason }) => orderService.updateOrderStatus(id, status, reason),
    onSuccess: () => {
      queryClient.invalidateQueries(['orders'])
      setShowForm(false)
      setEditingOrder(null)
    }
  })

  // Note: Django doesn't have delete order endpoint, so we'll disable delete functionality
  // Delete order mutation - disabled for Django backend
  // const deleteMutation = useMutation({
  //   mutationFn: orderService.deleteOrder,
  //   onSuccess: () => {
  //     queryClient.invalidateQueries(['orders'])
  //     setShowDeleteDialog(false)
  //     setDeletingOrder(null)
  //   }
  // })

  // Bulk update mutation (implement using individual status updates)
  const bulkUpdateMutation = useMutation({
    mutationFn: async ({ ids, status }) => {
      // Update orders one by one since Django might not support bulk update
      await Promise.all(ids.map(id => orderService.updateOrderStatus(id, status)))
    },
    onSuccess: () => {
      queryClient.invalidateQueries(['orders'])
      setBulkAction({ ids: [], action: '' })
    }
  })

  const getStatusIcon = (status) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'IN_PROGRESS':
        return <Clock className="h-4 w-4 text-blue-600" />
      case 'CONFIRMED':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800'
      case 'CONFIRMED':
        return 'bg-yellow-100 text-yellow-800'
      case 'CANCELLED':
        return 'bg-red-100 text-red-800'
      case 'PENDING':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const columns = [
    {
      key: 'order_number',
      title: 'Order #',
      render: (value, order) => (
        <div>
          <div className="font-medium text-gray-900">{value}</div>
          <div className="text-sm text-gray-500">
            {new Date(order.created_at).toLocaleDateString()}
          </div>
        </div>
      )
    },
    {
      key: 'customer_name',
      title: 'Customer',
      render: (value, order) => (
        <div className="flex items-center space-x-3">
          <div className="h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-white">
              {value.split(' ').map(n => n[0]).join('').toUpperCase()}
            </span>
          </div>
          <div>
            <div className="font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500 flex items-center space-x-1">
              <Phone className="h-3 w-3" />
              <span>{order.customer_phone}</span>
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'service_name',
      title: 'Service',
      render: (value, order) => (
        <div>
          <div className="font-medium text-gray-900">{value}</div>
          <div className="text-sm text-gray-500">{order.provider_name}</div>
        </div>
      )
    },
    {
      key: 'status',
      title: 'Status',
      render: (value) => (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(value)}`}>
          {getStatusIcon(value)}
          <span className="ml-1">{value.replace('_', ' ')}</span>
        </span>
      )
    },
    {
      key: 'total_amount',
      title: 'Amount',
      render: (value) => (
        <div className="flex items-center space-x-1">
          <IndianRupee className="h-4 w-4 text-gray-400" />
          <span className="font-medium">{value.toLocaleString()}</span>
        </div>
      )
    },
    {
      key: 'payment_status',
      title: 'Payment',
      render: (value) => (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          value === 'PAID' ? 'bg-green-100 text-green-800' :
          value === 'REFUNDED' ? 'bg-blue-100 text-blue-800' :
          'bg-yellow-100 text-yellow-800'
        }`}>
          {value}
        </span>
      )
    },
    {
      key: 'booking_date',
      title: 'Scheduled',
      render: (value) => (
        <div className="flex items-center space-x-1">
          <Calendar className="h-4 w-4 text-gray-400" />
          <span>{new Date(value).toLocaleDateString()}</span>
        </div>
      )
    }
  ]

  const formFields = [
    {
      name: 'status',
      label: 'Order Status',
      type: 'select',
      required: true,
      options: [
        { value: 'PENDING', label: 'Pending' },
        { value: 'CONFIRMED', label: 'Confirmed' },
        { value: 'IN_PROGRESS', label: 'In Progress' },
        { value: 'COMPLETED', label: 'Completed' },
        { value: 'CANCELLED', label: 'Cancelled' }
      ]
    },
    {
      name: 'payment_status',
      label: 'Payment Status',
      type: 'select',
      required: true,
      options: [
        { value: 'PENDING', label: 'Pending' },
        { value: 'PAID', label: 'Paid' },
        { value: 'REFUNDED', label: 'Refunded' }
      ]
    },
    {
      name: 'booking_date',
      label: 'Scheduled Date & Time',
      type: 'datetime-local',
      required: true
    },
    {
      name: 'notes',
      label: 'Notes',
      type: 'textarea',
      placeholder: 'Add any notes about this order',
      fullWidth: true,
      rows: 3
    }
  ]

  const handleEdit = (order) => {
    setEditingOrder({
      ...order,
      booking_date: new Date(order.booking_date).toISOString().slice(0, 16)
    })
    setShowForm(true)
  }

  const handleView = (order) => {
    setViewingOrder(order)
    setShowViewModal(true)
  }

  const handleDelete = (order) => {
    setDeletingOrder(order)
    setShowDeleteDialog(true)
  }

  const handleBulkAction = (ids, action) => {
    setBulkAction({ ids, action })
    if (action === 'delete') {
      setShowDeleteDialog(true)
    } else {
      // Handle status updates
      bulkUpdateMutation.mutate({ ids, status: action })
    }
  }

  const handleFormSubmit = (formData) => {
    const processedData = {
      ...formData,
      booking_date: new Date(formData.booking_date).toISOString()
    }

    if (formData.status === 'COMPLETED' && !editingOrder.completion_date) {
      processedData.completion_date = new Date().toISOString()
    }

    updateMutation.mutate({ id: editingOrder.id, ...processedData })
  }

  const handleConfirmDelete = () => {
    if (bulkAction.ids.length > 0 && bulkAction.action === 'delete') {
      // Handle bulk delete if implemented
      setBulkAction({ ids: [], action: '' })
      setShowDeleteDialog(false)
    } else if (deletingOrder) {
      deleteMutation.mutate(deletingOrder.id)
    }
  }

  const handleExport = () => {
    const csvContent = [
      ['Order #', 'Customer', 'Service', 'Provider', 'Status', 'Amount', 'Payment', 'Scheduled', 'Created'].join(','),
      ...orders.map(order => [
        order.order_number,
        order.customer_name,
        order.service_name,
        order.provider_name,
        order.status,
        order.total_amount,
        order.payment_status,
        new Date(order.booking_date).toLocaleDateString(),
        new Date(order.created_at).toLocaleDateString()
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'orders.csv'
    a.click()
    window.URL.revokeObjectURL(url)
  }

  // Custom bulk actions for orders
  const customBulkActions = (selectedIds) => (
    <div className="flex items-center space-x-2">
      <span className="text-sm text-gray-600">{selectedIds.length} selected</span>
      <select
        onChange={(e) => {
          if (e.target.value) {
            handleBulkAction(selectedIds, e.target.value)
            e.target.value = ''
          }
        }}
        className="text-sm border border-gray-300 rounded-md px-2 py-1"
      >
        <option value="">Bulk Actions</option>
        <option value="CONFIRMED">Mark as Confirmed</option>
        <option value="IN_PROGRESS">Mark as In Progress</option>
        <option value="COMPLETED">Mark as Completed</option>
        <option value="CANCELLED">Mark as Cancelled</option>
        <option value="delete">Delete Orders</option>
      </select>
    </div>
  )

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Order Management</h1>
          <p className="text-gray-600">
            Track and manage service orders
          </p>
        </div>
        <div className="flex items-center space-x-4 text-sm text-gray-600">
          <div className="flex items-center space-x-1">
            <Receipt className="h-4 w-4" />
            <span>Total: {orders.length}</span>
          </div>
          <div className="flex items-center space-x-1">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span>Completed: {orders.filter(o => o.status === 'COMPLETED').length}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="h-4 w-4 text-blue-600" />
            <span>Active: {orders.filter(o => ['CONFIRMED', 'IN_PROGRESS'].includes(o.status)).length}</span>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={orders}
        columns={columns}
        title="Orders"
        loading={isLoading}
        onEdit={handleEdit}
        onView={handleView}
        onDelete={handleDelete}
        onExport={handleExport}
        onRefresh={refetch}
        searchable
        sortable
        selectable
        filterable
        customBulkActions={customBulkActions}
      />

      {/* Edit Form Modal */}
      <DynamicForm
        isOpen={showForm}
        onClose={() => {
          setShowForm(false)
          setEditingOrder(null)
        }}
        onSubmit={handleFormSubmit}
        title="Update Order"
        fields={formFields}
        initialData={editingOrder}
        loading={updateMutation.isPending}
        submitText="Update Order"
      />

      {/* View Order Modal */}
      <Modal
        isOpen={showViewModal}
        onClose={() => {
          setShowViewModal(false)
          setViewingOrder(null)
        }}
        title={`Order Details - ${viewingOrder?.order_number}`}
        size="lg"
      >
        {viewingOrder && (
          <div className="space-y-6">
            {/* Order Status */}
            <div className="flex items-center justify-between">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(viewingOrder.status)}`}>
                {getStatusIcon(viewingOrder.status)}
                <span className="ml-2">{viewingOrder.status.replace('_', ' ')}</span>
              </span>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                viewingOrder.payment_status === 'PAID' ? 'bg-green-100 text-green-800' :
                viewingOrder.payment_status === 'REFUNDED' ? 'bg-blue-100 text-blue-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {viewingOrder.payment_status}
              </span>
            </div>

            {/* Customer Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Customer Information</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <span>{viewingOrder.customer_name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span>{viewingOrder.customer_phone}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span>{viewingOrder.address}</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Service Details</h3>
                <div className="space-y-2">
                  <div><strong>Service:</strong> {viewingOrder.service_name}</div>
                  <div><strong>Provider:</strong> {viewingOrder.provider_name}</div>
                  <div className="flex items-center space-x-2">
                    <IndianRupee className="h-4 w-4 text-gray-400" />
                    <span className="font-medium">₹{viewingOrder.total_amount.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Dates */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Timeline</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <div className="text-sm text-gray-500">Order Created</div>
                  <div>{new Date(viewingOrder.created_at).toLocaleString()}</div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Scheduled</div>
                  <div>{new Date(viewingOrder.booking_date).toLocaleString()}</div>
                </div>
                {viewingOrder.completion_date && (
                  <div>
                    <div className="text-sm text-gray-500">Completed</div>
                    <div>{new Date(viewingOrder.completion_date).toLocaleString()}</div>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            {viewingOrder.notes && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-3">Notes</h3>
                <p className="text-gray-600 bg-gray-50 p-3 rounded-md">{viewingOrder.notes}</p>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* Delete Confirmation */}
      <ConfirmDialog
        isOpen={showDeleteDialog}
        onClose={() => {
          setShowDeleteDialog(false)
          setDeletingOrder(null)
          setBulkAction({ ids: [], action: '' })
        }}
        onConfirm={handleConfirmDelete}
        title={bulkAction.ids.length > 0 ? 'Delete Multiple Orders' : 'Delete Order'}
        message={
          bulkAction.ids.length > 0
            ? `Are you sure you want to delete ${bulkAction.ids.length} selected orders? This action cannot be undone.`
            : `Are you sure you want to delete order "${deletingOrder?.order_number}"? This action cannot be undone.`
        }
        confirmText="Delete"
        type="danger"
        loading={deleteMutation.isPending}
      />
    </div>
  )
}

export default OrdersPage

