import api from './api'

export const authService = {
  // Login with email/mobile and password
  login: async (credentials) => {
    // Use email login for staff users
    const response = await api.post('/auth/login/email/', {
      email: credentials.identifier,
      password: credentials.password
    })

    // Transform the response to match expected format
    const data = response.data
    return {
      user: data.user,
      access: data.tokens.access,
      refresh: data.tokens.refresh
    }
  },

  // Logout
  logout: async () => {
    const refreshToken = localStorage.getItem('refresh_token')
    if (refreshToken) {
      try {
        await api.post('/auth/logout/', { refresh: refreshToken })
      } catch (error) {
        console.error('Logout error:', error)
      }
    }
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
  },

  // Get current user profile
  getCurrentUser: async () => {
    const response = await api.get('/auth/profile/')
    return response.data
  },

  // Refresh token
  refreshToken: async (refreshToken) => {
    const response = await api.post('/auth/token/refresh/', {
      refresh: refreshToken,
    })
    return response.data
  },

  // Verify token
  verifyToken: async (token) => {
    const response = await api.post('/auth/token/verify/', {
      token,
    })
    return response.data
  },
}

export const userService = {
  // Get all users with pagination and filters
  getUsers: async (params = {}) => {
    const response = await api.get('/auth/users/', { params })
    return response.data
  },

  // Get user by ID
  getUser: async (id) => {
    const response = await api.get(`/auth/users/${id}/`)
    return response.data
  },

  // Create new user
  createUser: async (userData) => {
    const response = await api.post('/auth/users/', userData)
    return response.data
  },

  // Update user
  updateUser: async (id, userData) => {
    const response = await api.put(`/auth/users/${id}/`, userData)
    return response.data
  },

  // Delete user
  deleteUser: async (id) => {
    const response = await api.delete(`/auth/users/${id}/`)
    return response.data
  },

  // Get user addresses
  getUserAddresses: async (userId) => {
    const response = await api.get(`/auth/users/${userId}/addresses/`)
    return response.data
  },

  // Lock/unlock user account
  toggleUserLock: async (id, isLocked) => {
    const response = await api.patch(`/auth/users/${id}/toggle-lock/`, {
      is_locked: isLocked,
    })
    return response.data
  },
}

export const categoryService = {
  // Get all categories
  getCategories: async (params = {}) => {
    const response = await api.get('/catalogue/categories/', { params })
    return response.data
  },

  // Get category by ID
  getCategory: async (id) => {
    const response = await api.get(`/catalogue/categories/${id}/`)
    return response.data
  },

  // Create category
  createCategory: async (categoryData) => {
    const response = await api.post('/catalogue/categories/', categoryData)
    return response.data
  },

  // Update category
  updateCategory: async (id, categoryData) => {
    const response = await api.put(`/catalogue/categories/${id}/`, categoryData)
    return response.data
  },

  // Delete category
  deleteCategory: async (id) => {
    const response = await api.delete(`/catalogue/categories/${id}/`)
    return response.data
  },

  // Get category tree
  getCategoryTree: async () => {
    const response = await api.get('/catalogue/categories/tree/')
    return response.data
  },
}

export const serviceService = {
  // Get all services
  getServices: async (params = {}) => {
    const response = await api.get('/catalogue/services/', { params })
    return response.data
  },

  // Get service by ID
  getService: async (id) => {
    const response = await api.get(`/catalogue/services/${id}/`)
    return response.data
  },

  // Create service
  createService: async (serviceData) => {
    const response = await api.post('/catalogue/services/', serviceData)
    return response.data
  },

  // Update service
  updateService: async (id, serviceData) => {
    const response = await api.put(`/catalogue/services/${id}/`, serviceData)
    return response.data
  },

  // Delete service
  deleteService: async (id) => {
    const response = await api.delete(`/catalogue/services/${id}/`)
    return response.data
  },

  // Get services by category
  getServicesByCategory: async (categoryId, params = {}) => {
    const response = await api.get(`/catalogue/categories/${categoryId}/services/`, {
      params,
    })
    return response.data
  },
}

export const orderService = {
  // Get all orders
  getOrders: async (params = {}) => {
    const response = await api.get('/orders/', { params })
    return response.data
  },

  // Get order by ID
  getOrder: async (id) => {
    const response = await api.get(`/orders/${id}/`)
    return response.data
  },

  // Update order status
  updateOrderStatus: async (id, status, reason = '') => {
    const response = await api.patch(`/orders/${id}/status/`, {
      status,
      reason,
    })
    return response.data
  },

  // Assign provider to order
  assignProvider: async (orderId, providerId) => {
    const response = await api.patch(`/orders/${orderId}/assign-provider/`, {
      provider_id: providerId,
    })
    return response.data
  },

  // Get order status history
  getOrderStatusHistory: async (orderId) => {
    const response = await api.get(`/orders/${orderId}/status-history/`)
    return response.data
  },

  // Cancel order
  cancelOrder: async (orderId, reason, description = '') => {
    const response = await api.post(`/orders/${orderId}/cancel/`, {
      reason,
      description,
    })
    return response.data
  },

  // Reschedule order
  rescheduleOrder: async (orderId, newDate, newTimeSlot, reason = '') => {
    const response = await api.post(`/orders/${orderId}/reschedule/`, {
      new_date: newDate,
      new_time_slot: newTimeSlot,
      reason,
    })
    return response.data
  },
}

export const providerService = {
  // Get all providers
  getProviders: async (params = {}) => {
    const response = await api.get('/providers/', { params })
    return response.data
  },

  // Get provider by ID
  getProvider: async (id) => {
    const response = await api.get(`/providers/${id}/`)
    return response.data
  },

  // Update provider verification status
  updateProviderVerification: async (id, status, reason = '') => {
    const response = await api.patch(`/providers/${id}/verification/`, {
      verification_status: status,
      reason,
    })
    return response.data
  },

  // Get provider documents
  getProviderDocuments: async (providerId) => {
    const response = await api.get(`/providers/${providerId}/documents/`)
    return response.data
  },

  // Verify provider document
  verifyProviderDocument: async (documentId, status, reason = '') => {
    const response = await api.patch(`/providers/documents/${documentId}/verify/`, {
      verification_status: status,
      rejection_reason: reason,
    })
    return response.data
  },

  // Get provider bank details
  getProviderBankDetails: async (providerId) => {
    const response = await api.get(`/providers/${providerId}/bank-details/`)
    return response.data
  },

  // Verify provider bank details
  verifyProviderBankDetails: async (providerId, isVerified) => {
    const response = await api.patch(`/providers/${providerId}/bank-details/verify/`, {
      is_verified: isVerified,
    })
    return response.data
  },

  // Get provider payout requests
  getProviderPayoutRequests: async (params = {}) => {
    const response = await api.get('/providers/payout-requests/', { params })
    return response.data
  },

  // Process payout request
  processPayoutRequest: async (requestId, status, notes = '') => {
    const response = await api.patch(`/providers/payout-requests/${requestId}/process/`, {
      status,
      admin_notes: notes,
    })
    return response.data
  },
}

export const paymentService = {
  // Get all payment transactions
  getPaymentTransactions: async (params = {}) => {
    const response = await api.get('/payments/transactions/', { params })
    return response.data
  },

  // Get payment transaction by ID
  getPaymentTransaction: async (id) => {
    const response = await api.get(`/payments/transactions/${id}/`)
    return response.data
  },

  // Get payment configuration
  getPaymentConfiguration: async () => {
    const response = await api.get('/payments/configuration/')
    return response.data
  },

  // Update payment configuration
  updatePaymentConfiguration: async (configData) => {
    const response = await api.put('/payments/configuration/', configData)
    return response.data
  },

  // Get payment refunds
  getPaymentRefunds: async (params = {}) => {
    const response = await api.get('/payments/refunds/', { params })
    return response.data
  },

  // Process refund
  processRefund: async (transactionId, amount, reason) => {
    const response = await api.post(`/payments/transactions/${transactionId}/refund/`, {
      amount,
      reason,
    })
    return response.data
  },

  // Get payment webhooks
  getPaymentWebhooks: async (params = {}) => {
    const response = await api.get('/payments/webhooks/', { params })
    return response.data
  },
}

export const couponService = {
  // Get all coupons
  getCoupons: async (params = {}) => {
    const response = await api.get('/coupons/', { params })
    return response.data
  },

  // Get coupon by ID
  getCoupon: async (id) => {
    const response = await api.get(`/coupons/${id}/`)
    return response.data
  },

  // Create coupon
  createCoupon: async (couponData) => {
    const response = await api.post('/coupons/', couponData)
    return response.data
  },

  // Update coupon
  updateCoupon: async (id, couponData) => {
    const response = await api.put(`/coupons/${id}/`, couponData)
    return response.data
  },

  // Delete coupon
  deleteCoupon: async (id) => {
    const response = await api.delete(`/coupons/${id}/`)
    return response.data
  },

  // Get coupon usage statistics
  getCouponUsage: async (couponId) => {
    const response = await api.get(`/coupons/${couponId}/usage/`)
    return response.data
  },
}

export const schedulingService = {
  // Get slot configurations
  getSlotConfigurations: async () => {
    const response = await api.get('/scheduling/configurations/')
    return response.data
  },

  // Update slot configuration
  updateSlotConfiguration: async (id, configData) => {
    const response = await api.put(`/scheduling/configurations/${id}/`, configData)
    return response.data
  },

  // Get working shifts
  getWorkingShifts: async (configId) => {
    const response = await api.get(`/scheduling/configurations/${configId}/shifts/`)
    return response.data
  },

  // Update working shifts
  updateWorkingShifts: async (configId, shiftsData) => {
    const response = await api.put(`/scheduling/configurations/${configId}/shifts/`, shiftsData)
    return response.data
  },

  // Get holiday schedules
  getHolidaySchedules: async (params = {}) => {
    const response = await api.get('/scheduling/holidays/', { params })
    return response.data
  },

  // Create holiday
  createHoliday: async (holidayData) => {
    const response = await api.post('/scheduling/holidays/', holidayData)
    return response.data
  },

  // Update holiday
  updateHoliday: async (id, holidayData) => {
    const response = await api.put(`/scheduling/holidays/${id}/`, holidayData)
    return response.data
  },

  // Delete holiday
  deleteHoliday: async (id) => {
    const response = await api.delete(`/scheduling/holidays/${id}/`)
    return response.data
  },

  // Get time slots
  getTimeSlots: async (params = {}) => {
    const response = await api.get('/scheduling/slots/', { params })
    return response.data
  },

  // Block time slots
  blockTimeSlots: async (blockageData) => {
    const response = await api.post('/scheduling/slots/block/', blockageData)
    return response.data
  },

  // Get slot bookings
  getSlotBookings: async (params = {}) => {
    const response = await api.get('/scheduling/bookings/', { params })
    return response.data
  },
}

export const taxationService = {
  // Get tax categories
  getTaxCategories: async () => {
    const response = await api.get('/taxation/categories/')
    return response.data
  },

  // Create tax category
  createTaxCategory: async (categoryData) => {
    const response = await api.post('/taxation/categories/', categoryData)
    return response.data
  },

  // Update tax category
  updateTaxCategory: async (id, categoryData) => {
    const response = await api.put(`/taxation/categories/${id}/`, categoryData)
    return response.data
  },

  // Delete tax category
  deleteTaxCategory: async (id) => {
    const response = await api.delete(`/taxation/categories/${id}/`)
    return response.data
  },

  // Get GST rates
  getGSTRates: async (params = {}) => {
    const response = await api.get('/taxation/gst-rates/', { params })
    return response.data
  },

  // Create GST rate
  createGSTRate: async (rateData) => {
    const response = await api.post('/taxation/gst-rates/', rateData)
    return response.data
  },

  // Update GST rate
  updateGSTRate: async (id, rateData) => {
    const response = await api.put(`/taxation/gst-rates/${id}/`, rateData)
    return response.data
  },

  // Delete GST rate
  deleteGSTRate: async (id) => {
    const response = await api.delete(`/taxation/gst-rates/${id}/`)
    return response.data
  },

  // Get tax configurations
  getTaxConfigurations: async () => {
    const response = await api.get('/taxation/configurations/')
    return response.data
  },

  // Update tax configuration
  updateTaxConfiguration: async (id, configData) => {
    const response = await api.put(`/taxation/configurations/${id}/`, configData)
    return response.data
  },

  // Get tax calculations
  getTaxCalculations: async (params = {}) => {
    const response = await api.get('/taxation/calculations/', { params })
    return response.data
  },
}

export const dashboardService = {
  // Get dashboard statistics
  getDashboardStats: async () => {
    const response = await api.get('/orders/dashboard/')
    return response.data
  },

  // Get recent activities (using recent orders from dashboard)
  getRecentActivities: async (limit = 10) => {
    const response = await api.get('/orders/dashboard/')
    return {
      activities: response.data.recent_orders?.slice(0, limit) || []
    }
  },

  // Get revenue analytics (placeholder - implement when Django endpoint is available)
  getRevenueAnalytics: async (period = '30d') => {
    // For now, return mock data structure until Django endpoint is implemented
    return {
      total_revenue: 0,
      period_revenue: 0,
      growth_percentage: 0,
      chart_data: []
    }
  },

  // Get order analytics (using dashboard data)
  getOrderAnalytics: async (period = '30d') => {
    const response = await api.get('/orders/dashboard/')
    return {
      total_orders: response.data.total_orders,
      pending_orders: response.data.pending_orders,
      confirmed_orders: response.data.confirmed_orders,
      in_progress_orders: response.data.in_progress_orders,
      completed_orders: response.data.completed_orders,
      cancelled_orders: response.data.cancelled_orders
    }
  },
}

